//
//  DataManager.swift
//  TimeScale
//
//  Created by MeeYou on 2025/8/25.
//

import Foundation
import SwiftData
import UniformTypeIdentifiers

@MainActor
class DataManager: ObservableObject {
    static let shared = DataManager()
    
    @Published var isExporting = false
    @Published var isImporting = false
    @Published var lastExportDate: Date?
    @Published var lastImportDate: Date?
    
    private init() {
        loadLastOperationDates()
    }
    
    // MARK: - 数据导出
    
    /// 导出所有数据为JSON
    func exportData(from modelContext: ModelContext) async throws -> URL {
        isExporting = true
        defer { isExporting = false }
        
        // 获取所有数据
        let habits = try modelContext.fetch(FetchDescriptor<Habit>())
        let habitLogs = try modelContext.fetch(FetchDescriptor<HabitLog>())
        let goals = try modelContext.fetch(FetchDescriptor<Goal>())
        let goalCheckIns = try modelContext.fetch(FetchDescriptor<GoalCheckIn>())
        let timeCategories = try modelContext.fetch(FetchDescriptor<TimeCategory>())
        let timeEntries = try modelContext.fetch(FetchDescriptor<TimeEntry>())
        
        // 创建导出数据结构
        let exportData = ExportData(
            version: "1.0",
            exportDate: Date(),
            habits: habits.map(ExportHabit.init),
            habitLogs: habitLogs.map(ExportHabitLog.init),
            goals: goals.map(ExportGoal.init),
            goalCheckIns: goalCheckIns.map(ExportGoalCheckIn.init),
            timeCategories: timeCategories.map(ExportTimeCategory.init),
            timeEntries: timeEntries.map(ExportTimeEntry.init)
        )
        
        // 转换为JSON
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = .prettyPrinted
        
        let jsonData = try encoder.encode(exportData)
        
        // 保存到临时文件
        let fileName = "TimeScale_Export_\(DateFormatter.fileNameFormatter.string(from: Date())).json"
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        
        try jsonData.write(to: tempURL)
        
        // 更新最后导出时间
        lastExportDate = Date()
        saveLastOperationDates()
        
        return tempURL
    }
    
    // MARK: - 数据导入
    
    /// 从JSON文件导入数据
    func importData(from url: URL, to modelContext: ModelContext, replaceExisting: Bool = false) async throws {
        isImporting = true
        defer { isImporting = false }
        
        // 读取文件
        let jsonData = try Data(contentsOf: url)
        
        // 解析JSON
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        let importData = try decoder.decode(ExportData.self, from: jsonData)
        
        // 验证版本兼容性
        guard isVersionCompatible(importData.version) else {
            throw DataImportError.incompatibleVersion(importData.version)
        }
        
        // 如果需要替换现有数据，先清除
        if replaceExisting {
            try clearAllData(from: modelContext)
        }
        
        // 导入数据
        try await importHabitsAndLogs(importData, to: modelContext)
        try await importGoalsAndCheckIns(importData, to: modelContext)
        try await importTimeCategoriesAndEntries(importData, to: modelContext)
        
        // 保存更改
        try modelContext.save()
        
        // 更新最后导入时间
        lastImportDate = Date()
        saveLastOperationDates()
    }
    
    // MARK: - 数据验证
    
    /// 验证导入文件
    func validateImportFile(at url: URL) async throws -> ImportValidationResult {
        let jsonData = try Data(contentsOf: url)
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        let importData = try decoder.decode(ExportData.self, from: jsonData)
        
        return ImportValidationResult(
            version: importData.version,
            exportDate: importData.exportDate,
            habitsCount: importData.habits.count,
            goalsCount: importData.goals.count,
            timeEntriesCount: importData.timeEntries.count,
            isCompatible: isVersionCompatible(importData.version)
        )
    }
    
    // MARK: - 私有方法
    
    private func isVersionCompatible(_ version: String) -> Bool {
        // 简单的版本兼容性检查
        return version.hasPrefix("1.")
    }
    
    private func clearAllData(from modelContext: ModelContext) throws {
        // 删除所有数据
        try modelContext.delete(model: Habit.self)
        try modelContext.delete(model: HabitLog.self)
        try modelContext.delete(model: Goal.self)
        try modelContext.delete(model: GoalCheckIn.self)
        try modelContext.delete(model: TimeCategory.self)
        try modelContext.delete(model: TimeEntry.self)
    }
    
    private func importHabitsAndLogs(_ data: ExportData, to modelContext: ModelContext) async throws {
        // 创建习惯ID映射
        var habitIdMap: [String: Habit] = [:]
        
        // 导入习惯
        for exportHabit in data.habits {
            let habit = Habit(
                name: exportHabit.name,
                colorHex: exportHabit.colorHex,
                scheduleRule: exportHabit.scheduleRule
            )
            habit.customWeekdays = exportHabit.customWeekdays
            habit.reminderTimes = exportHabit.reminderTimes
            habit.archived = exportHabit.archived
            habit.createdAt = exportHabit.createdAt
            habit.updatedAt = exportHabit.updatedAt
            
            modelContext.insert(habit)
            habitIdMap[exportHabit.id] = habit
        }
        
        // 导入习惯记录
        for exportLog in data.habitLogs {
            let log = HabitLog(date: exportLog.date, done: exportLog.done, note: exportLog.note)
            log.createdAt = exportLog.createdAt
            
            if let habit = habitIdMap[exportLog.habitId] {
                log.habit = habit
            }
            
            modelContext.insert(log)
        }
    }
    
    private func importGoalsAndCheckIns(_ data: ExportData, to modelContext: ModelContext) async throws {
        // 创建目标ID映射
        var goalIdMap: [String: Goal] = [:]
        
        // 导入目标
        for exportGoal in data.goals {
            let goal = Goal(
                title: exportGoal.title,
                unit: exportGoal.unit,
                period: exportGoal.period,
                target: exportGoal.target,
                deadline: exportGoal.deadline
            )
            goal.archived = exportGoal.archived
            goal.createdAt = exportGoal.createdAt
            goal.updatedAt = exportGoal.updatedAt
            
            modelContext.insert(goal)
            goalIdMap[exportGoal.id] = goal
        }
        
        // 导入目标打卡记录
        for exportCheckIn in data.goalCheckIns {
            let checkIn = GoalCheckIn(amount: exportCheckIn.amount, note: exportCheckIn.note)
            checkIn.timestamp = exportCheckIn.timestamp
            
            if let goal = goalIdMap[exportCheckIn.goalId] {
                checkIn.goal = goal
            }
            
            modelContext.insert(checkIn)
        }
    }
    
    private func importTimeCategoriesAndEntries(_ data: ExportData, to modelContext: ModelContext) async throws {
        // 创建分类ID映射
        var categoryIdMap: [String: TimeCategory] = [:]
        
        // 导入时间分类
        for exportCategory in data.timeCategories {
            let category = TimeCategory(
                name: exportCategory.name,
                colorHex: exportCategory.colorHex,
                order: exportCategory.order
            )
            category.archived = exportCategory.archived
            category.createdAt = exportCategory.createdAt
            
            modelContext.insert(category)
            categoryIdMap[exportCategory.id] = category
        }
        
        // 导入时间记录
        for exportEntry in data.timeEntries {
            let entry = TimeEntry(start: exportEntry.start, note: exportEntry.note)
            entry.end = exportEntry.end
            entry.durationSec = exportEntry.durationSec
            entry.isRunning = exportEntry.isRunning
            entry.createdAt = exportEntry.createdAt
            
            if let categoryId = exportEntry.categoryId,
               let category = categoryIdMap[categoryId] {
                entry.category = category
            }
            
            modelContext.insert(entry)
        }
    }
    
    private func loadLastOperationDates() {
        lastExportDate = UserDefaults.standard.object(forKey: "lastExportDate") as? Date
        lastImportDate = UserDefaults.standard.object(forKey: "lastImportDate") as? Date
    }
    
    private func saveLastOperationDates() {
        if let lastExportDate = lastExportDate {
            UserDefaults.standard.set(lastExportDate, forKey: "lastExportDate")
        }
        if let lastImportDate = lastImportDate {
            UserDefaults.standard.set(lastImportDate, forKey: "lastImportDate")
        }
    }
}

// MARK: - 数据结构

struct ExportData: Codable {
    let version: String
    let exportDate: Date
    let habits: [ExportHabit]
    let habitLogs: [ExportHabitLog]
    let goals: [ExportGoal]
    let goalCheckIns: [ExportGoalCheckIn]
    let timeCategories: [ExportTimeCategory]
    let timeEntries: [ExportTimeEntry]
}

struct ExportHabit: Codable {
    let id: String
    let name: String
    let colorHex: String
    let scheduleRule: ScheduleRule
    let customWeekdays: [Int]
    let reminderTimes: [DateComponents]
    let archived: Bool
    let createdAt: Date
    let updatedAt: Date
    
    init(from habit: Habit) {
        self.id = habit.id.uuidString
        self.name = habit.name
        self.colorHex = habit.colorHex
        self.scheduleRule = habit.scheduleRule
        self.customWeekdays = habit.customWeekdays
        self.reminderTimes = habit.reminderTimes
        self.archived = habit.archived
        self.createdAt = habit.createdAt
        self.updatedAt = habit.updatedAt
    }
}

struct ExportHabitLog: Codable {
    let id: String
    let habitId: String
    let date: Date
    let done: Bool
    let note: String?
    let createdAt: Date
    
    init(from log: HabitLog) {
        self.id = log.id.uuidString
        self.habitId = log.habit?.id.uuidString ?? ""
        self.date = log.date
        self.done = log.done
        self.note = log.note
        self.createdAt = log.createdAt
    }
}

struct ExportGoal: Codable {
    let id: String
    let title: String
    let unit: String
    let period: GoalPeriod
    let target: Double
    let deadline: Date?
    let archived: Bool
    let createdAt: Date
    let updatedAt: Date
    
    init(from goal: Goal) {
        self.id = goal.id.uuidString
        self.title = goal.title
        self.unit = goal.unit
        self.period = goal.period
        self.target = goal.target
        self.deadline = goal.deadline
        self.archived = goal.archived
        self.createdAt = goal.createdAt
        self.updatedAt = goal.updatedAt
    }
}

struct ExportGoalCheckIn: Codable {
    let id: String
    let goalId: String
    let timestamp: Date
    let amount: Double
    let note: String?
    
    init(from checkIn: GoalCheckIn) {
        self.id = checkIn.id.uuidString
        self.goalId = checkIn.goal?.id.uuidString ?? ""
        self.timestamp = checkIn.timestamp
        self.amount = checkIn.amount
        self.note = checkIn.note
    }
}

struct ExportTimeCategory: Codable {
    let id: String
    let name: String
    let colorHex: String
    let order: Int
    let archived: Bool
    let createdAt: Date
    
    init(from category: TimeCategory) {
        self.id = category.id.uuidString
        self.name = category.name
        self.colorHex = category.colorHex
        self.order = category.order
        self.archived = category.archived
        self.createdAt = category.createdAt
    }
}

struct ExportTimeEntry: Codable {
    let id: String
    let categoryId: String?
    let start: Date
    let end: Date?
    let durationSec: Double
    let note: String?
    let isRunning: Bool
    let createdAt: Date
    
    init(from entry: TimeEntry) {
        self.id = entry.id.uuidString
        self.categoryId = entry.category?.id.uuidString
        self.start = entry.start
        self.end = entry.end
        self.durationSec = entry.durationSec
        self.note = entry.note
        self.isRunning = entry.isRunning
        self.createdAt = entry.createdAt
    }
}

struct ImportValidationResult {
    let version: String
    let exportDate: Date
    let habitsCount: Int
    let goalsCount: Int
    let timeEntriesCount: Int
    let isCompatible: Bool
}

// MARK: - DateFormatter扩展

extension DateFormatter {
    static let fileNameFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        return formatter
    }()
}

enum DataImportError: LocalizedError {
    case incompatibleVersion(String)
    case invalidFormat
    case corruptedData
    
    var errorDescription: String? {
        switch self {
        case .incompatibleVersion(let version):
            return "不兼容的数据版本: \(version)"
        case .invalidFormat:
            return "无效的文件格式"
        case .corruptedData:
            return "数据文件已损坏"
        }
    }
}


