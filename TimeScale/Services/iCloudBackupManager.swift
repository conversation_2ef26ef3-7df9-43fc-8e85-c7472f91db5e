//
//  iCloudBackupManager.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import Foundation
import SwiftData
import CloudKit
import CryptoKit
import UIKit

/// iCloud备份管理器 - 负责数据的云端备份、同步和恢复
@MainActor
class iCloudBackupManager: ObservableObject {
    static let shared = iCloudBackupManager()
    
    // MARK: - 状态属性
    @Published var isBackingUp = false
    @Published var isRestoring = false
    @Published var isSyncing = false
    @Published var lastBackupDate: Date?
    @Published var lastSyncDate: Date?
    @Published var availableBackups: [BackupMetadata] = []
    @Published var iCloudAvailable = false
    @Published var syncEnabled = false
    
    // MARK: - 私有属性
    private let container: CKContainer
    private let database: CKDatabase
    private let backupRecordType = "TimeScaleBackup"
    private let metadataRecordType = "BackupMetadata"
    private let userDefaults = UserDefaults.standard
    
    private init() {
        self.container = CKContainer(identifier: "iCloud.com.timescale.app")
        self.database = container.privateCloudDatabase
        
        loadSettings()
        checkiCloudAvailability()
    }
    
    // MARK: - 公共方法
    
    /// 执行备份到iCloud
    func performBackup(from modelContext: ModelContext, isIncremental: Bool = true) async throws {
        guard iCloudAvailable else {
            throw iCloudBackupError.iCloudUnavailable
        }
        
        isBackingUp = true
        defer { isBackingUp = false }
        
        do {
            // 1. 导出数据
            let exportURL = try await DataManager.shared.exportData(from: modelContext)
            let exportData = try Data(contentsOf: exportURL)
            
            // 2. 创建备份元数据
            let metadata = BackupMetadata(
                id: UUID().uuidString,
                version: getCurrentAppVersion(),
                dataVersion: "1.0",
                createdAt: Date(),
                size: exportData.count,
                isIncremental: isIncremental,
                deviceName: await getDeviceName(),
                checksum: calculateChecksum(for: exportData)
            )
            
            // 3. 上传到iCloud
            try await uploadBackup(data: exportData, metadata: metadata)
            
            // 4. 更新本地状态
            lastBackupDate = Date()
            saveSettings()
            
            // 5. 清理旧备份（保留最近10个）
            try await cleanupOldBackups()
            
            // 6. 刷新可用备份列表
            try await refreshAvailableBackups()
            
        } catch {
            throw iCloudBackupError.backupFailed(error.localizedDescription)
        }
    }
    
    /// 从iCloud恢复数据
    func restoreFromBackup(_ metadata: BackupMetadata, to modelContext: ModelContext, replaceExisting: Bool = false) async throws {
        guard iCloudAvailable else {
            throw iCloudBackupError.iCloudUnavailable
        }
        
        isRestoring = true
        defer { isRestoring = false }
        
        do {
            // 1. 从iCloud下载备份数据
            let backupData = try await downloadBackup(metadata: metadata)
            
            // 2. 验证数据完整性
            let downloadedChecksum = calculateChecksum(for: backupData)
            guard downloadedChecksum == metadata.checksum else {
                throw iCloudBackupError.dataCorrupted
            }
            
            // 3. 检查版本兼容性
            try validateVersionCompatibility(metadata.dataVersion)
            
            // 4. 创建临时文件
            let tempURL = FileManager.default.temporaryDirectory
                .appendingPathComponent("restore_\(metadata.id).json")
            try backupData.write(to: tempURL)
            
            // 5. 导入数据
            try await DataManager.shared.importData(
                from: tempURL,
                to: modelContext,
                replaceExisting: replaceExisting
            )
            
            // 6. 清理临时文件
            try? FileManager.default.removeItem(at: tempURL)
            
        } catch {
            throw iCloudBackupError.restoreFailed(error.localizedDescription)
        }
    }
    
    /// 启用/禁用自动同步
    func setSyncEnabled(_ enabled: Bool) {
        syncEnabled = enabled
        saveSettings()
        
        if enabled {
            Task {
                try await performAutoSync()
            }
        }
    }
    
    /// 执行自动同步
    func performAutoSync() async throws {
        guard syncEnabled && iCloudAvailable else { return }
        
        isSyncing = true
        defer { isSyncing = false }
        
        // 检查是否需要同步（距离上次同步超过1小时）
        if let lastSync = lastSyncDate,
           Date().timeIntervalSince(lastSync) < 3600 {
            return
        }
        
        // 执行增量备份作为同步
        // 这里可以根据需要实现更复杂的同步逻辑
        lastSyncDate = Date()
        saveSettings()
    }
    
    /// 刷新可用备份列表
    func refreshAvailableBackups() async throws {
        guard iCloudAvailable else { return }
        
        let query = CKQuery(recordType: metadataRecordType, predicate: NSPredicate(value: true))
        query.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
        
        do {
            let (records, _) = try await database.records(matching: query)
            
            availableBackups = records.compactMap { (recordID, result) in
                switch result {
                case .success(let record):
                    return BackupMetadata(from: record)
                case .failure:
                    return nil
                }
            }
        } catch {
            throw iCloudBackupError.fetchFailed(error.localizedDescription)
        }
    }
    
    /// 删除指定备份
    func deleteBackup(_ metadata: BackupMetadata) async throws {
        guard iCloudAvailable else { return }
        
        let metadataRecordID = CKRecord.ID(recordName: metadata.id)
        let backupRecordID = CKRecord.ID(recordName: "backup_\(metadata.id)")
        
        try await database.deleteRecord(withID: metadataRecordID)
        try await database.deleteRecord(withID: backupRecordID)
        
        // 从本地列表中移除
        availableBackups.removeAll { $0.id == metadata.id }
    }
    
    // MARK: - 私有方法
    
    private func checkiCloudAvailability() {
        Task {
            do {
                let status = try await container.accountStatus()
                await MainActor.run {
                    iCloudAvailable = (status == .available)
                }
            } catch {
                await MainActor.run {
                    iCloudAvailable = false
                }
            }
        }
    }
    
    private func uploadBackup(data: Data, metadata: BackupMetadata) async throws {
        // 1. 创建备份数据记录
        let backupRecord = CKRecord(recordType: backupRecordType, recordID: CKRecord.ID(recordName: "backup_\(metadata.id)"))
        backupRecord["data"] = CKAsset(fileURL: try createTempFile(with: data))
        
        // 2. 创建元数据记录
        let metadataRecord = CKRecord(recordType: metadataRecordType, recordID: CKRecord.ID(recordName: metadata.id))
        metadataRecord["version"] = metadata.version
        metadataRecord["dataVersion"] = metadata.dataVersion
        metadataRecord["createdAt"] = metadata.createdAt
        metadataRecord["size"] = metadata.size
        metadataRecord["isIncremental"] = metadata.isIncremental ? 1 : 0
        metadataRecord["deviceName"] = metadata.deviceName
        metadataRecord["checksum"] = metadata.checksum
        
        // 3. 批量上传
        let operation = CKModifyRecordsOperation(recordsToSave: [backupRecord, metadataRecord])
        operation.savePolicy = .allKeys
        
        try await withCheckedThrowingContinuation { continuation in
            operation.modifyRecordsResultBlock = { result in
                switch result {
                case .success:
                    continuation.resume()
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
            database.add(operation)
        }
    }
    
    private func downloadBackup(metadata: BackupMetadata) async throws -> Data {
        let recordID = CKRecord.ID(recordName: "backup_\(metadata.id)")
        let record = try await database.record(for: recordID)
        
        guard let asset = record["data"] as? CKAsset,
              let fileURL = asset.fileURL else {
            throw iCloudBackupError.downloadFailed("备份文件不存在")
        }
        
        return try Data(contentsOf: fileURL)
    }
    
    private func createTempFile(with data: Data) throws -> URL {
        let tempURL = FileManager.default.temporaryDirectory
            .appendingPathComponent(UUID().uuidString)
            .appendingPathExtension("json")
        try data.write(to: tempURL)
        return tempURL
    }
    
    private func calculateChecksum(for data: Data) -> String {
        return data.sha256
    }
    
    private func getCurrentAppVersion() -> String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    }
    
    private func getDeviceName() async -> String {
        return await UIDevice.current.name
    }
    
    private func validateVersionCompatibility(_ dataVersion: String) throws {
        // 简单的版本兼容性检查
        guard dataVersion.hasPrefix("1.") else {
            throw iCloudBackupError.incompatibleVersion(dataVersion)
        }
    }
    
    private func cleanupOldBackups() async throws {
        // 保留最近10个备份，删除更老的
        let sortedBackups = availableBackups.sorted { $0.createdAt > $1.createdAt }
        let backupsToDelete = Array(sortedBackups.dropFirst(10))
        
        for backup in backupsToDelete {
            try await deleteBackup(backup)
        }
    }
    
    private func loadSettings() {
        lastBackupDate = userDefaults.object(forKey: "lastBackupDate") as? Date
        lastSyncDate = userDefaults.object(forKey: "lastSyncDate") as? Date
        syncEnabled = userDefaults.bool(forKey: "syncEnabled")
    }
    
    private func saveSettings() {
        if let lastBackupDate = lastBackupDate {
            userDefaults.set(lastBackupDate, forKey: "lastBackupDate")
        }
        if let lastSyncDate = lastSyncDate {
            userDefaults.set(lastSyncDate, forKey: "lastSyncDate")
        }
        userDefaults.set(syncEnabled, forKey: "syncEnabled")
    }
}

// MARK: - 数据结构

/// 备份元数据
struct BackupMetadata: Identifiable, Codable {
    let id: String
    let version: String          // 应用版本
    let dataVersion: String      // 数据格式版本
    let createdAt: Date
    let size: Int               // 备份文件大小（字节）
    let isIncremental: Bool     // 是否为增量备份
    let deviceName: String      // 创建备份的设备名称
    let checksum: String        // 数据校验和

    init(id: String, version: String, dataVersion: String, createdAt: Date,
         size: Int, isIncremental: Bool, deviceName: String, checksum: String) {
        self.id = id
        self.version = version
        self.dataVersion = dataVersion
        self.createdAt = createdAt
        self.size = size
        self.isIncremental = isIncremental
        self.deviceName = deviceName
        self.checksum = checksum
    }

    /// 从CloudKit记录创建
    init?(from record: CKRecord) {
        guard let version = record["version"] as? String,
              let dataVersion = record["dataVersion"] as? String,
              let createdAt = record["createdAt"] as? Date,
              let size = record["size"] as? Int,
              let isIncrementalValue = record["isIncremental"] as? Int,
              let deviceName = record["deviceName"] as? String,
              let checksum = record["checksum"] as? String else {
            return nil
        }

        self.id = record.recordID.recordName
        self.version = version
        self.dataVersion = dataVersion
        self.createdAt = createdAt
        self.size = size
        self.isIncremental = isIncrementalValue == 1
        self.deviceName = deviceName
        self.checksum = checksum
    }

    /// 格式化文件大小
    var formattedSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: Int64(size))
    }

    /// 格式化创建时间
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
}

/// iCloud备份错误类型
enum iCloudBackupError: LocalizedError {
    case iCloudUnavailable
    case backupFailed(String)
    case restoreFailed(String)
    case downloadFailed(String)
    case fetchFailed(String)
    case dataCorrupted
    case incompatibleVersion(String)
    case quotaExceeded
    case networkError

    var errorDescription: String? {
        switch self {
        case .iCloudUnavailable:
            return "iCloud不可用，请检查网络连接和iCloud设置"
        case .backupFailed(let message):
            return "备份失败：\(message)"
        case .restoreFailed(let message):
            return "恢复失败：\(message)"
        case .downloadFailed(let message):
            return "下载失败：\(message)"
        case .fetchFailed(let message):
            return "获取备份列表失败：\(message)"
        case .dataCorrupted:
            return "备份数据已损坏，无法恢复"
        case .incompatibleVersion(let version):
            return "备份版本(\(version))与当前应用不兼容"
        case .quotaExceeded:
            return "iCloud存储空间不足"
        case .networkError:
            return "网络连接错误"
        }
    }
}

// MARK: - 扩展

extension Data {
    /// 计算SHA256校验和
    var sha256: String {
        let hashed = SHA256.hash(data: self)
        return hashed.compactMap { String(format: "%02x", $0) }.joined()
    }
}
