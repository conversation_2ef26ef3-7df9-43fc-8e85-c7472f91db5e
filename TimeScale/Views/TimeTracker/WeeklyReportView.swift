//
//  WeeklyReportView.swift
//  TimeScale
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/25.
//

import SwiftUI
import SwiftData
import Charts

struct WeeklyReportView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var timeEntries: [TimeEntry]
    @Query private var categories: [TimeCategory]
    
    @State private var selectedWeek = Date()
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 周选择器
                    weekSelector
                    
                    // 总时长卡片
                    totalTimeCard
                    
                    // 每日时长柱状图
                    dailyTimeChart
                    
                    // 分类占比环图
                    categoryPieChart
                    
                    // Top 3 分类卡片
                    topCategoriesSection
                }
                .padding()
            }
            .navigationTitle(LocalizedStringKey("周报"))
        }
    }
    
    // MARK: - 周选择器
    private var weekSelector: some View {
        HStack {
            Button {
                selectedWeek = Calendar.current.date(byAdding: .weekOfYear, value: -1, to: selectedWeek) ?? selectedWeek
            } label: {
                Image(systemName: "chevron.left")
                    .font(.title2)
            }
            
            Spacer()
            
            Text(weekRangeText)
                .font(.headline)
                .fontWeight(.medium)
            
            Spacer()
            
            Button {
                selectedWeek = Calendar.current.date(byAdding: .weekOfYear, value: 1, to: selectedWeek) ?? selectedWeek
            } label: {
                Image(systemName: "chevron.right")
                    .font(.title2)
            }
            .disabled(isCurrentWeek)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - 总时长卡片
    private var totalTimeCard: some View {
        VStack(spacing: 8) {
            Text("本周总时长")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text(formatDuration(weeklyTotalDuration))
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.blue)
            
            HStack {
                Text("日均: \(formatDuration(weeklyTotalDuration / 7))")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if weeklyTotalDuration > 0 {
                    Text("活跃天数: \(activeDaysCount)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - 每日时长柱状图
    private var dailyTimeChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("每日时长分布")
                .font(.headline)
            
            Chart(dailyData, id: \.date) { data in
                BarMark(
                    x: .value("日期", data.date, unit: .day),
                    y: .value("时长", data.duration / 3600) // 转换为小时
                )
                .foregroundStyle(.blue)
                .cornerRadius(4)
            }
            .frame(height: 200)
            .chartYAxis {
                AxisMarks(position: .leading) { value in
                    AxisValueLabel {
                        if let hours = value.as(Double.self) {
                            Text("\(Int(hours))h")
                        }
                    }
                }
            }
            .chartXAxis {
                AxisMarks(values: .stride(by: .day)) { value in
                    AxisValueLabel(format: .dateTime.weekday(.abbreviated))
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 分类占比环图
    private var categoryPieChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("分类时长占比")
                .font(.headline)
            
            if categoryData.isEmpty {
                Text("本周暂无记录")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(height: 200)
                    .frame(maxWidth: .infinity)
            } else {
                Chart(categoryData, id: \.category.id) { data in
                    SectorMark(
                        angle: .value("时长", data.duration),
                        innerRadius: .ratio(0.5),
                        angularInset: 2
                    )
                    .foregroundStyle(Color(hex: data.category.colorHex))
                    .opacity(0.8)
                }
                .frame(height: 200)
                .chartLegend(position: .bottom, alignment: .center) {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                        ForEach(categoryData, id: \.category.id) { data in
                            HStack(spacing: 6) {
                                Circle()
                                    .fill(Color(hex: data.category.colorHex))
                                    .frame(width: 8, height: 8)
                                
                                Text(data.category.name)
                                    .font(.caption)
                                    .lineLimit(1)
                                
                                Spacer()
                                
                                Text(formatDuration(data.duration))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - Top 3 分类
    private var topCategoriesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("时长排行")
                .font(.headline)
            
            if categoryData.isEmpty {
                Text("本周暂无记录")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(categoryData.prefix(3).enumerated()), id: \.element.category.id) { index, data in
                        TopCategoryRowView(
                            rank: index + 1,
                            category: data.category,
                            duration: data.duration,
                            percentage: data.duration / weeklyTotalDuration
                        )
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 计算属性
    private var weekRange: (start: Date, end: Date) {
        let calendar = Calendar.current
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: selectedWeek)?.start ?? selectedWeek
        let endOfWeek = calendar.date(byAdding: .day, value: 6, to: startOfWeek) ?? selectedWeek
        return (startOfWeek, endOfWeek)
    }
    
    private var weekRangeText: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日"
        return "\(formatter.string(from: weekRange.start)) - \(formatter.string(from: weekRange.end))"
    }
    
    private var isCurrentWeek: Bool {
        let calendar = Calendar.current
        return calendar.isDate(selectedWeek, equalTo: Date(), toGranularity: .weekOfYear)
    }
    
    private var weeklyEntries: [TimeEntry] {
        timeEntries.filter { entry in
            !entry.isRunning && entry.start >= weekRange.start && entry.start <= weekRange.end
        }
    }
    
    private var weeklyTotalDuration: TimeInterval {
        weeklyEntries.reduce(0) { $0 + $1.durationSec }
    }
    
    private var activeDaysCount: Int {
        let calendar = Calendar.current
        let uniqueDays = Set(weeklyEntries.map { calendar.startOfDay(for: $0.start) })
        return uniqueDays.count
    }
    
    private var dailyData: [DailyTimeData] {
        let calendar = Calendar.current
        var data: [DailyTimeData] = []
        
        for i in 0..<7 {
            let date = calendar.date(byAdding: .day, value: i, to: weekRange.start) ?? weekRange.start
            let dayEntries = weeklyEntries.filter { calendar.isDate($0.start, inSameDayAs: date) }
            let duration = dayEntries.reduce(0) { $0 + $1.durationSec }
            data.append(DailyTimeData(date: date, duration: duration))
        }
        
        return data
    }
    
    private var categoryData: [CategoryTimeData] {
        let grouped = Dictionary(grouping: weeklyEntries) { $0.category }
        
        return grouped.compactMap { category, entries in
            guard let category = category else { return nil }
            let duration = entries.reduce(0) { $0 + $1.durationSec }
            return CategoryTimeData(category: category, duration: duration)
        }
        .sorted { $0.duration > $1.duration }
    }
    
    // MARK: - 辅助方法
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

// MARK: - 数据结构
struct DailyTimeData {
    let date: Date
    let duration: TimeInterval
}

struct CategoryTimeData {
    let category: TimeCategory
    let duration: TimeInterval
}

// MARK: - Top分类行视图
struct TopCategoryRowView: View {
    let rank: Int
    let category: TimeCategory
    let duration: TimeInterval
    let percentage: Double
    
    var body: some View {
        HStack {
            // 排名
            Text("\(rank)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(rankColor)
                .clipShape(Circle())
            
            // 分类信息
            HStack(spacing: 8) {
                Circle()
                    .fill(Color(hex: category.colorHex))
                    .frame(width: 12, height: 12)
                
                Text(category.name)
                    .font(.body)
                    .fontWeight(.medium)
            }
            
            Spacer()
            
            // 时长和百分比
            VStack(alignment: .trailing, spacing: 2) {
                Text(formatDuration(duration))
                    .font(.body)
                    .fontWeight(.medium)
                
                Text("\(Int(percentage * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.white)
        .cornerRadius(8)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private var rankColor: Color {
        switch rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .blue
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

#Preview {
    WeeklyReportView()
        .modelContainer(for: [TimeEntry.self, TimeCategory.self], inMemory: true)
}
