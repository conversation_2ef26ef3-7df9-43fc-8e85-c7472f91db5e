//
//  TimeOverviewView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/27.
//

import SwiftUI
import SwiftData
import Charts

// MARK: - 时间报告类型枚举
enum TimeReportType: String, CaseIterable {
    case daily = "日报"
    case weekly = "周报"
    case monthly = "月报"
    case yearly = "年报"
    
    var displayName: String {
        return self.rawValue
    }
    
    var icon: String {
        switch self {
        case .daily: return "calendar"
        case .weekly: return "calendar.badge.clock"
        case .monthly: return "calendar.badge.plus"
        case .yearly: return "calendar.badge.exclamationmark"
        }
    }
    
    var calendarComponent: Calendar.Component {
        switch self {
        case .daily: return .day
        case .weekly: return .weekOfYear
        case .monthly: return .month
        case .yearly: return .year
        }
    }
}

// MARK: - 时间总览视图
struct TimeOverviewView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var timeEntries: [TimeEntry]
    @Query private var categories: [TimeCategory]
    
    @State private var selectedReportType: TimeReportType = .weekly
    @State private var selectedDate = Date()
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 报告类型选择器
                    reportTypeSelector
                    
                    // 日期选择器
                    dateSelector
                    
                    // 总览卡片
                    overviewCards
                    
                    // 主要图表
                    mainChart
                    
                    // 分类分析
                    categoryAnalysis
                    
                    // 详细统计
                    detailedStats
                }
                .padding()
            }
            .navigationTitle("时间总览")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    // MARK: - 报告类型选择器
    private var reportTypeSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("报告类型")
                .font(.headline)
            
            Picker("报告类型", selection: $selectedReportType) {
                ForEach(TimeReportType.allCases, id: \.self) { type in
                    HStack {
                        Image(systemName: type.icon)
                        Text(type.displayName)
                    }
                    .tag(type)
                }
            }
            .pickerStyle(.segmented)
        }
    }
    
    // MARK: - 日期选择器
    private var dateSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(dateRangeDescription)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack {
                Button(action: { adjustDate(-1) }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.blue)
                }
                
                Spacer()
                
                DatePicker(
                    "",
                    selection: $selectedDate,
                    displayedComponents: datePickerComponents
                )
                .labelsHidden()
                
                Spacer()
                
                Button(action: { adjustDate(1) }) {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal)
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 总览卡片
    private var overviewCards: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
            StatCard(
                title: "总时长",
                value: formatDuration(totalDuration),
                unit: "",
                color: .blue
            )
            
            StatCard(
                title: activeDaysTitle,
                value: "\(activeDaysCount)",
                unit: activeDaysUnit,
                color: .green
            )
            
            StatCard(
                title: "平均时长",
                value: formatDuration(averageDuration),
                unit: "",
                color: .orange
            )
            
            StatCard(
                title: "记录次数",
                value: "\(entriesCount)",
                unit: "次",
                color: .purple
            )
        }
    }
    
    // MARK: - 主要图表
    private var mainChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(chartTitle)
                .font(.headline)
            
            Chart(chartData) { data in
                BarMark(
                    x: .value("时间", data.label),
                    y: .value("时长", data.duration / 3600) // 转换为小时
                )
                .foregroundStyle(.blue)
                .cornerRadius(4)
            }
            .frame(height: 200)
            .chartYAxis {
                AxisMarks(position: .leading) { value in
                    AxisValueLabel {
                        if let hours = value.as(Double.self) {
                            Text("\(Int(hours))h")
                        }
                    }
                }
            }
            .chartXAxis {
                AxisMarks { value in
                    AxisGridLine()
                    AxisValueLabel(angle: chartLabelAngle)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 分类分析
    private var categoryAnalysis: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("分类分析")
                .font(.headline)
            
            if categoryData.isEmpty {
                Text("暂无数据")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
            } else {
                // 环形图
                Chart(categoryData) { data in
                    SectorMark(
                        angle: .value("时长", data.duration),
                        innerRadius: .ratio(0.5),
                        angularInset: 2
                    )
                    .foregroundStyle(Color(hex: data.category.colorHex))
                    .opacity(0.8)
                }
                .frame(height: 200)
                
                // 分类列表
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                    ForEach(categoryData.prefix(6), id: \.category.id) { data in
                        CategoryStatRow(
                            category: data.category,
                            duration: data.duration,
                            percentage: data.duration / totalDuration
                        )
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 详细统计
    private var detailedStats: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("详细统计")
                .font(.headline)
            
            VStack(spacing: 12) {
                DetailStatRow(
                    title: "最长单次记录",
                    value: formatDuration(longestEntry),
                    icon: "clock.badge.checkmark"
                )
                
                DetailStatRow(
                    title: "最短单次记录",
                    value: formatDuration(shortestEntry),
                    icon: "clock.badge.xmark"
                )
                
                DetailStatRow(
                    title: "最活跃分类",
                    value: mostActiveCategory,
                    icon: "star.fill"
                )
                
                DetailStatRow(
                    title: periodSpecificStat.title,
                    value: periodSpecificStat.value,
                    icon: periodSpecificStat.icon
                )
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }

    // MARK: - 计算属性

    private var dateRange: (start: Date, end: Date) {
        let calendar = Calendar.current

        switch selectedReportType {
        case .daily:
            let start = calendar.startOfDay(for: selectedDate)
            let end = calendar.date(byAdding: .day, value: 1, to: start)?.addingTimeInterval(-1) ?? start
            return (start, end)

        case .weekly:
            let weekInterval = calendar.dateInterval(of: .weekOfYear, for: selectedDate)
            return (weekInterval?.start ?? selectedDate, weekInterval?.end ?? selectedDate)

        case .monthly:
            let monthInterval = calendar.dateInterval(of: .month, for: selectedDate)
            return (monthInterval?.start ?? selectedDate, monthInterval?.end ?? selectedDate)

        case .yearly:
            let yearInterval = calendar.dateInterval(of: .year, for: selectedDate)
            return (yearInterval?.start ?? selectedDate, yearInterval?.end ?? selectedDate)
        }
    }

    private var filteredEntries: [TimeEntry] {
        timeEntries.filter { entry in
            !entry.isRunning &&
            entry.start >= dateRange.start &&
            entry.start <= dateRange.end
        }
    }

    private var totalDuration: TimeInterval {
        filteredEntries.reduce(0) { $0 + $1.durationSec }
    }

    private var activeDaysCount: Int {
        let calendar = Calendar.current
        let uniqueDays = Set(filteredEntries.map { calendar.startOfDay(for: $0.start) })
        return uniqueDays.count
    }

    private var averageDuration: TimeInterval {
        guard !filteredEntries.isEmpty else { return 0 }
        return totalDuration / Double(filteredEntries.count)
    }

    private var entriesCount: Int {
        filteredEntries.count
    }

    private var activeDaysTitle: String {
        switch selectedReportType {
        case .daily: return "今日记录"
        case .weekly: return "活跃天数"
        case .monthly: return "活跃天数"
        case .yearly: return "活跃天数"
        }
    }

    private var activeDaysUnit: String {
        switch selectedReportType {
        case .daily: return "次"
        case .weekly, .monthly, .yearly: return "天"
        }
    }

    private var dateRangeDescription: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")

        switch selectedReportType {
        case .daily:
            formatter.dateFormat = "yyyy年M月d日"
            return formatter.string(from: selectedDate)

        case .weekly:
            formatter.dateFormat = "M月d日"
            return "\(formatter.string(from: dateRange.start)) - \(formatter.string(from: dateRange.end))"

        case .monthly:
            formatter.dateFormat = "yyyy年M月"
            return formatter.string(from: selectedDate)

        case .yearly:
            formatter.dateFormat = "yyyy年"
            return formatter.string(from: selectedDate)
        }
    }

    private var datePickerComponents: DatePickerComponents {
        switch selectedReportType {
        case .daily: return [.date]
        case .weekly: return [.date]
        case .monthly: return [.date]
        case .yearly: return [.date]
        }
    }

    private var chartTitle: String {
        switch selectedReportType {
        case .daily: return "小时分布"
        case .weekly: return "每日时长"
        case .monthly: return "每日时长"
        case .yearly: return "每月时长"
        }
    }

    private var chartLabelAngle: Angle {
        switch selectedReportType {
        case .daily: return .degrees(0)
        case .weekly: return .degrees(-45)
        case .monthly: return .degrees(-45)
        case .yearly: return .degrees(0)
        }
    }

    private var chartData: [TimeChartData] {
        let calendar = Calendar.current

        switch selectedReportType {
        case .daily:
            return generateHourlyData()
        case .weekly:
            return generateDailyData(in: dateRange)
        case .monthly:
            return generateDailyData(in: dateRange)
        case .yearly:
            return generateMonthlyData()
        }
    }

    private var categoryData: [CategoryTimeData] {
        let grouped = Dictionary(grouping: filteredEntries) { $0.category }

        return grouped.compactMap { category, entries in
            guard let category = category else { return nil }
            let duration = entries.reduce(0) { $0 + $1.durationSec }
            return CategoryTimeData(category: category, duration: duration)
        }
        .sorted { $0.duration > $1.duration }
    }

    private var longestEntry: TimeInterval {
        filteredEntries.map { $0.durationSec }.max() ?? 0
    }

    private var shortestEntry: TimeInterval {
        filteredEntries.map { $0.durationSec }.min() ?? 0
    }

    private var mostActiveCategory: String {
        categoryData.first?.category.name ?? "无"
    }

    private var periodSpecificStat: (title: String, value: String, icon: String) {
        switch selectedReportType {
        case .daily:
            let peakHour = findPeakHour()
            return ("最活跃时段", "\(peakHour):00", "clock.fill")

        case .weekly:
            let peakDay = findPeakDay()
            return ("最活跃日", peakDay, "calendar.badge.clock")

        case .monthly:
            let avgDaily = totalDuration / Double(max(1, activeDaysCount))
            return ("日均时长", formatDuration(avgDaily), "chart.bar.fill")

        case .yearly:
            let peakMonth = findPeakMonth()
            return ("最活跃月", peakMonth, "calendar.badge.plus")
        }
    }

    // MARK: - 数据生成方法

    private func generateHourlyData() -> [TimeChartData] {
        var hourlyData: [Int: TimeInterval] = [:]

        for entry in filteredEntries {
            let hour = Calendar.current.component(.hour, from: entry.start)
            hourlyData[hour, default: 0] += entry.durationSec
        }

        return (0...23).map { hour in
            TimeChartData(
                label: "\(hour)",
                duration: hourlyData[hour] ?? 0
            )
        }
    }

    private func generateDailyData(in range: (start: Date, end: Date)) -> [TimeChartData] {
        let calendar = Calendar.current
        var dailyData: [Date: TimeInterval] = [:]

        for entry in filteredEntries {
            let day = calendar.startOfDay(for: entry.start)
            dailyData[day, default: 0] += entry.durationSec
        }

        var result: [TimeChartData] = []
        var currentDate = range.start

        while currentDate <= range.end {
            let dayStart = calendar.startOfDay(for: currentDate)
            let duration = dailyData[dayStart] ?? 0

            let formatter = DateFormatter()
            formatter.locale = Locale(identifier: "zh_CN")

            let label: String
            if selectedReportType == .weekly {
                formatter.dateFormat = "E"
                label = formatter.string(from: currentDate)
            } else {
                formatter.dateFormat = "d"
                label = formatter.string(from: currentDate)
            }

            result.append(TimeChartData(label: label, duration: duration))
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }

        return result
    }

    private func generateMonthlyData() -> [TimeChartData] {
        let calendar = Calendar.current
        var monthlyData: [Int: TimeInterval] = [:]

        for entry in filteredEntries {
            let month = calendar.component(.month, from: entry.start)
            monthlyData[month, default: 0] += entry.durationSec
        }

        return (1...12).map { month in
            let formatter = DateFormatter()
            formatter.locale = Locale(identifier: "zh_CN")
            formatter.dateFormat = "M月"

            let date = calendar.date(from: DateComponents(month: month)) ?? Date()
            let label = formatter.string(from: date)

            return TimeChartData(
                label: label,
                duration: monthlyData[month] ?? 0
            )
        }
    }

    // MARK: - 辅助方法

    private func adjustDate(_ direction: Int) {
        let calendar = Calendar.current

        switch selectedReportType {
        case .daily:
            selectedDate = calendar.date(byAdding: .day, value: direction, to: selectedDate) ?? selectedDate
        case .weekly:
            selectedDate = calendar.date(byAdding: .weekOfYear, value: direction, to: selectedDate) ?? selectedDate
        case .monthly:
            selectedDate = calendar.date(byAdding: .month, value: direction, to: selectedDate) ?? selectedDate
        case .yearly:
            selectedDate = calendar.date(byAdding: .year, value: direction, to: selectedDate) ?? selectedDate
        }
    }

    private func findPeakHour() -> Int {
        var hourlyData: [Int: TimeInterval] = [:]

        for entry in filteredEntries {
            let hour = Calendar.current.component(.hour, from: entry.start)
            hourlyData[hour, default: 0] += entry.durationSec
        }

        return hourlyData.max(by: { $0.value < $1.value })?.key ?? 0
    }

    private func findPeakDay() -> String {
        let calendar = Calendar.current
        var dailyData: [Date: TimeInterval] = [:]

        for entry in filteredEntries {
            let day = calendar.startOfDay(for: entry.start)
            dailyData[day, default: 0] += entry.durationSec
        }

        guard let peakDate = dailyData.max(by: { $0.value < $1.value })?.key else {
            return "无"
        }

        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "M月d日"
        return formatter.string(from: peakDate)
    }

    private func findPeakMonth() -> String {
        let calendar = Calendar.current
        var monthlyData: [Int: TimeInterval] = [:]

        for entry in filteredEntries {
            let month = calendar.component(.month, from: entry.start)
            monthlyData[month, default: 0] += entry.durationSec
        }

        guard let peakMonth = monthlyData.max(by: { $0.value < $1.value })?.key else {
            return "无"
        }

        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "M月"

        let date = calendar.date(from: DateComponents(month: peakMonth)) ?? Date()
        return formatter.string(from: date)
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        return TimeAnalysisUtils.formatDuration(duration)
    }
}

// MARK: - 辅助组件

struct CategoryStatRow: View {
    let category: TimeCategory
    let duration: TimeInterval
    let percentage: Double

    var body: some View {
        HStack(spacing: 8) {
            Circle()
                .fill(Color(hex: category.colorHex))
                .frame(width: 12, height: 12)

            VStack(alignment: .leading, spacing: 2) {
                Text(category.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .lineLimit(1)

                Text(formatDuration(duration))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Text("\(Int(percentage * 100))%")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 8)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        return duration.formattedShort()
    }
}

struct DetailStatRow: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.headline)
                    .fontWeight(.medium)
            }

            Spacer()
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: TimeEntry.self, TimeCategory.self, configurations: config)

    let category = TimeCategory(name: "工作", colorHex: "#007AFF", order: 1)
    container.mainContext.insert(category)

    return TimeOverviewView()
        .modelContainer(container)
}
