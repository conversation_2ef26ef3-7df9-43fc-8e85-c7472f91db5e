//
//  PeriodAnalysisView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/27.
//

import SwiftUI
import SwiftData
import Charts

// MARK: - 周期类型枚举
enum AnalysisPeriod: String, CaseIterable {
    case daily = "日"
    case weekly = "周"
    case monthly = "月"
    case quarterly = "季度"
    case yearly = "年"
    
    var displayName: String {
        return self.rawValue
    }
    
    var calendarComponent: Calendar.Component {
        switch self {
        case .daily: return .day
        case .weekly: return .weekOfYear
        case .monthly: return .month
        case .quarterly: return .month // 季度使用月份计算
        case .yearly: return .year
        }
    }
}

// MARK: - 周期数据模型
struct PeriodData: Identifiable {
    let id = UUID()
    let period: String
    let startDate: Date
    let endDate: Date
    let completedDays: Int
    let totalDays: Int
    let completionRate: Double
    let streakCount: Int
    let averageGap: Double // 平均间隔天数
    
    var formattedPeriod: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        
        let calendar = Calendar.current
        if calendar.isDate(startDate, equalTo: endDate, toGranularity: .month) {
            formatter.dateFormat = "yyyy年M月"
        } else if calendar.isDate(startDate, equalTo: endDate, toGranularity: .year) {
            formatter.dateFormat = "yyyy年"
        } else {
            formatter.dateFormat = "M/d"
        }
        
        return formatter.string(from: startDate)
    }
}

// MARK: - 周期分析视图
struct PeriodAnalysisView: View {
    let habit: Habit
    @State private var selectedPeriod: AnalysisPeriod = .monthly
    @State private var showingComparison = false
    @State private var comparisonPeriod: AnalysisPeriod = .weekly
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 周期选择器
                    periodSelector
                    
                    // 主要图表
                    mainChart
                    
                    // 对比分析（可选）
                    if showingComparison {
                        comparisonChart
                    }
                    
                    // 统计摘要
                    statisticsSummary
                    
                    // 趋势分析
                    trendAnalysis
                    
                    // 周期性洞察
                    periodicInsights
                }
                .padding()
            }
            .navigationTitle("\(habit.name) 周期分析")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(showingComparison ? "隐藏对比" : "显示对比") {
                        withAnimation {
                            showingComparison.toggle()
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - 周期选择器
    private var periodSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("分析周期")
                .font(.headline)
            
            Picker("分析周期", selection: $selectedPeriod) {
                ForEach(AnalysisPeriod.allCases, id: \.self) { period in
                    Text(period.displayName).tag(period)
                }
            }
            .pickerStyle(.segmented)
            
            if showingComparison {
                Text("对比周期")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Picker("对比周期", selection: $comparisonPeriod) {
                    ForEach(AnalysisPeriod.allCases, id: \.self) { period in
                        Text(period.displayName).tag(period)
                    }
                }
                .pickerStyle(.segmented)
            }
        }
    }
    
    // MARK: - 主要图表
    private var mainChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("\(selectedPeriod.displayName)度完成率趋势")
                .font(.headline)
            
            Chart(periodData) { data in
                // 完成率柱状图
                BarMark(
                    x: .value("周期", data.formattedPeriod),
                    y: .value("完成率", data.completionRate)
                )
                .foregroundStyle(Color(hex: habit.colorHex))
                .opacity(0.8)
                
                // 完成率趋势线
                LineMark(
                    x: .value("周期", data.formattedPeriod),
                    y: .value("完成率", data.completionRate)
                )
                .foregroundStyle(.orange)
                .lineStyle(StrokeStyle(lineWidth: 2))
                .symbol(Circle())
            }
            .frame(height: 200)
            .chartYScale(domain: 0...1)
            .chartYAxis {
                AxisMarks(values: [0, 0.25, 0.5, 0.75, 1]) { value in
                    AxisGridLine()
                    AxisValueLabel {
                        if let doubleValue = value.as(Double.self) {
                            Text("\(Int(doubleValue * 100))%")
                        }
                    }
                }
            }
            .chartXAxis {
                AxisMarks { value in
                    AxisGridLine()
                    AxisValueLabel(angle: .degrees(-45))
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 对比图表
    private var comparisonChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("\(selectedPeriod.displayName)度 vs \(comparisonPeriod.displayName)度对比")
                .font(.headline)
            
            Chart {
                // 主周期数据
                ForEach(periodData) { data in
                    BarMark(
                        x: .value("周期", data.formattedPeriod),
                        y: .value("完成率", data.completionRate)
                    )
                    .foregroundStyle(Color(hex: habit.colorHex))
                    .position(by: .value("类型", selectedPeriod.displayName))
                }
                
                // 对比周期数据
                ForEach(comparisonPeriodData) { data in
                    BarMark(
                        x: .value("周期", data.formattedPeriod),
                        y: .value("完成率", data.completionRate)
                    )
                    .foregroundStyle(.orange)
                    .position(by: .value("类型", comparisonPeriod.displayName))
                }
            }
            .frame(height: 200)
            .chartYScale(domain: 0...1)
            .chartYAxis {
                AxisMarks(values: [0, 0.5, 1]) { value in
                    AxisGridLine()
                    AxisValueLabel {
                        if let doubleValue = value.as(Double.self) {
                            Text("\(Int(doubleValue * 100))%")
                        }
                    }
                }
            }
            .chartXAxis {
                AxisMarks { value in
                    AxisGridLine()
                    AxisValueLabel(angle: .degrees(-45))
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 统计摘要
    private var statisticsSummary: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("统计摘要")
                .font(.headline)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                StatCard(
                    title: "平均完成率",
                    value: "\(Int(averageCompletionRate * 100))",
                    unit: "%",
                    color: .blue
                )
                
                StatCard(
                    title: "最佳\(selectedPeriod.displayName)度",
                    value: "\(Int(bestPeriodRate * 100))",
                    unit: "%",
                    color: .green
                )
                
                StatCard(
                    title: "稳定性",
                    value: "\(Int(stabilityScore * 100))",
                    unit: "%",
                    color: .purple,
                    subtitle: "完成率波动程度"
                )
                
                StatCard(
                    title: "改善趋势",
                    value: trendDirection,
                    unit: "",
                    color: trendColor,
                    subtitle: "最近趋势变化"
                )
            }
        }
    }
    
    // MARK: - 趋势分析
    private var trendAnalysis: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("趋势分析")
                .font(.headline)
            
            Chart(periodData) { data in
                LineMark(
                    x: .value("周期", data.formattedPeriod),
                    y: .value("连续天数", Double(data.streakCount))
                )
                .foregroundStyle(.red)
                .lineStyle(StrokeStyle(lineWidth: 2))
                .symbol(Circle())
                
                AreaMark(
                    x: .value("周期", data.formattedPeriod),
                    y: .value("连续天数", Double(data.streakCount))
                )
                .foregroundStyle(.red.opacity(0.2))
            }
            .frame(height: 150)
            .chartYAxis {
                AxisMarks(position: .leading) { value in
                    AxisGridLine()
                    AxisValueLabel()
                }
            }
            .chartXAxis {
                AxisMarks { value in
                    AxisGridLine()
                    AxisValueLabel(angle: .degrees(-45))
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 周期性洞察
    private var periodicInsights: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("周期性洞察")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 12) {
                InsightRow(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "表现最佳时期",
                    description: bestPeriodInsight,
                    color: .green
                )
                
                InsightRow(
                    icon: "chart.line.downtrend.xyaxis",
                    title: "需要改进时期",
                    description: worstPeriodInsight,
                    color: .orange
                )
                
                InsightRow(
                    icon: "arrow.triangle.2.circlepath",
                    title: "周期性模式",
                    description: patternInsight,
                    color: .blue
                )
                
                InsightRow(
                    icon: "lightbulb",
                    title: "建议",
                    description: recommendationInsight,
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }

    // MARK: - 计算属性

    private var periodData: [PeriodData] {
        generatePeriodData(for: selectedPeriod)
    }

    private var comparisonPeriodData: [PeriodData] {
        generatePeriodData(for: comparisonPeriod)
    }

    private var averageCompletionRate: Double {
        let rates = periodData.map { $0.completionRate }
        return rates.isEmpty ? 0 : rates.reduce(0, +) / Double(rates.count)
    }

    private var bestPeriodRate: Double {
        periodData.map { $0.completionRate }.max() ?? 0
    }

    private var stabilityScore: Double {
        let rates = periodData.map { $0.completionRate }
        guard rates.count > 1 else { return 1.0 }

        let mean = rates.reduce(0, +) / Double(rates.count)
        let variance = rates.map { pow($0 - mean, 2) }.reduce(0, +) / Double(rates.count)
        let standardDeviation = sqrt(variance)

        // 稳定性分数：标准差越小，稳定性越高
        return max(0, 1 - standardDeviation)
    }

    private var trendDirection: String {
        guard periodData.count >= 2 else { return "无数据" }

        let recent = Array(periodData.suffix(3))
        let earlier = Array(periodData.prefix(3))

        let recentAvg = recent.map { $0.completionRate }.reduce(0, +) / Double(recent.count)
        let earlierAvg = earlier.map { $0.completionRate }.reduce(0, +) / Double(earlier.count)

        let change = recentAvg - earlierAvg

        if change > 0.1 {
            return "显著上升"
        } else if change > 0.05 {
            return "轻微上升"
        } else if change < -0.1 {
            return "显著下降"
        } else if change < -0.05 {
            return "轻微下降"
        } else {
            return "保持稳定"
        }
    }

    private var trendColor: Color {
        switch trendDirection {
        case "显著上升", "轻微上升":
            return .green
        case "显著下降", "轻微下降":
            return .red
        default:
            return .blue
        }
    }

    private var bestPeriodInsight: String {
        guard let bestPeriod = periodData.max(by: { $0.completionRate < $1.completionRate }) else {
            return "暂无数据"
        }
        return "\(bestPeriod.formattedPeriod)表现最佳，完成率达到\(Int(bestPeriod.completionRate * 100))%"
    }

    private var worstPeriodInsight: String {
        guard let worstPeriod = periodData.min(by: { $0.completionRate < $1.completionRate }) else {
            return "暂无数据"
        }
        return "\(worstPeriod.formattedPeriod)需要改进，完成率仅为\(Int(worstPeriod.completionRate * 100))%"
    }

    private var patternInsight: String {
        // 分析周期性模式
        let rates = periodData.map { $0.completionRate }
        guard rates.count >= 4 else { return "数据不足，无法分析模式" }

        // 简单的周期性检测
        let firstHalf = Array(rates.prefix(rates.count / 2))
        let secondHalf = Array(rates.suffix(rates.count / 2))

        let firstAvg = firstHalf.reduce(0, +) / Double(firstHalf.count)
        let secondAvg = secondHalf.reduce(0, +) / Double(secondHalf.count)

        if secondAvg > firstAvg + 0.1 {
            return "整体呈上升趋势，后期表现更好"
        } else if firstAvg > secondAvg + 0.1 {
            return "前期表现较好，后期有所下降"
        } else {
            return "表现相对稳定，无明显周期性变化"
        }
    }

    private var recommendationInsight: String {
        let avgRate = averageCompletionRate
        let stability = stabilityScore

        if avgRate < 0.5 {
            return "建议降低目标难度或增加提醒频率"
        } else if stability < 0.7 {
            return "建议建立更规律的执行时间，提高稳定性"
        } else if avgRate > 0.8 && stability > 0.8 {
            return "表现优秀！可以考虑设置更具挑战性的目标"
        } else {
            return "保持当前节奏，继续努力提升完成率"
        }
    }

    // MARK: - 数据生成方法

    private func generatePeriodData(for period: AnalysisPeriod) -> [PeriodData] {
        let calendar = Calendar.current
        let now = Date()
        let logs = habit.logs.filter { $0.done }.sorted { $0.date < $1.date }

        // 根据周期类型确定时间范围
        let periodsCount: Int

        switch period {
        case .daily:
            periodsCount = 30 // 最近30天
        case .weekly:
            periodsCount = 12 // 最近12周
        case .monthly:
            periodsCount = 12 // 最近12个月
        case .quarterly:
            periodsCount = 8 // 最近8个季度
        case .yearly:
            periodsCount = 5 // 最近5年
        }

        var periodDataArray: [PeriodData] = []

        for i in 0..<periodsCount {
            let periodStart: Date
            let periodEnd: Date

            switch period {
            case .daily:
                periodStart = calendar.date(byAdding: .day, value: -i, to: now) ?? now
                periodEnd = periodStart
            case .weekly:
                let weekStart = calendar.dateInterval(of: .weekOfYear, for: calendar.date(byAdding: .weekOfYear, value: -i, to: now) ?? now)?.start ?? now
                periodStart = weekStart
                periodEnd = calendar.date(byAdding: .day, value: 6, to: weekStart) ?? weekStart
            case .monthly:
                let monthStart = calendar.dateInterval(of: .month, for: calendar.date(byAdding: .month, value: -i, to: now) ?? now)?.start ?? now
                periodStart = monthStart
                periodEnd = calendar.date(byAdding: .month, value: 1, to: monthStart)?.addingTimeInterval(-1) ?? monthStart
            case .quarterly:
                // 手动计算季度
                let quarterMonthsBack = i * 3
                let quarterStartMonth = calendar.date(byAdding: .month, value: -quarterMonthsBack, to: now) ?? now
                let quarterStart = calendar.dateInterval(of: .month, for: quarterStartMonth)?.start ?? now
                periodStart = quarterStart
                periodEnd = calendar.date(byAdding: .month, value: 3, to: quarterStart)?.addingTimeInterval(-1) ?? quarterStart
            case .yearly:
                let yearStart = calendar.dateInterval(of: .year, for: calendar.date(byAdding: .year, value: -i, to: now) ?? now)?.start ?? now
                periodStart = yearStart
                periodEnd = calendar.date(byAdding: .year, value: 1, to: yearStart)?.addingTimeInterval(-1) ?? yearStart
            }

            // 计算该周期内的完成情况
            let periodLogs = logs.filter { log in
                log.date >= calendar.startOfDay(for: periodStart) &&
                log.date <= calendar.startOfDay(for: periodEnd)
            }

            let completedDays = periodLogs.count
            let totalDays = calendar.dateComponents([.day], from: periodStart, to: min(periodEnd, now)).day ?? 1
            let completionRate = totalDays > 0 ? Double(completedDays) / Double(totalDays) : 0

            // 计算连续天数
            let streakCount = calculateStreakInPeriod(logs: periodLogs, start: periodStart, end: periodEnd)

            // 计算平均间隔
            let averageGap = calculateAverageGap(logs: periodLogs)

            let periodData = PeriodData(
                period: formatPeriod(start: periodStart, end: periodEnd, type: period),
                startDate: periodStart,
                endDate: periodEnd,
                completedDays: completedDays,
                totalDays: totalDays,
                completionRate: completionRate,
                streakCount: streakCount,
                averageGap: averageGap
            )

            periodDataArray.append(periodData)
        }

        return periodDataArray.reversed() // 按时间顺序排列
    }

    private func calculateStreakInPeriod(logs: [HabitLog], start: Date, end: Date) -> Int {
        let calendar = Calendar.current
        let sortedLogs = logs.sorted { $0.date < $1.date }

        var maxStreak = 0
        var currentStreak = 0
        var lastDate: Date?

        for log in sortedLogs {
            if let last = lastDate {
                let daysBetween = calendar.dateComponents([.day], from: last, to: log.date).day ?? 0
                if daysBetween == 1 {
                    currentStreak += 1
                } else {
                    currentStreak = 1
                }
            } else {
                currentStreak = 1
            }

            maxStreak = max(maxStreak, currentStreak)
            lastDate = log.date
        }

        return maxStreak
    }

    private func calculateAverageGap(logs: [HabitLog]) -> Double {
        guard logs.count > 1 else { return 0 }

        let calendar = Calendar.current
        let sortedLogs = logs.sorted { $0.date < $1.date }
        var gaps: [Int] = []

        for i in 1..<sortedLogs.count {
            let gap = calendar.dateComponents([.day], from: sortedLogs[i-1].date, to: sortedLogs[i].date).day ?? 0
            gaps.append(gap)
        }

        return gaps.isEmpty ? 0 : Double(gaps.reduce(0, +)) / Double(gaps.count)
    }

    private func formatPeriod(start: Date, end: Date, type: AnalysisPeriod) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")

        switch type {
        case .daily:
            formatter.dateFormat = "M/d"
            return formatter.string(from: start)
        case .weekly:
            formatter.dateFormat = "M/d"
            return "\(formatter.string(from: start))-\(formatter.string(from: end))"
        case .monthly:
            formatter.dateFormat = "yyyy年M月"
            return formatter.string(from: start)
        case .quarterly:
            let month = Calendar.current.component(.month, from: start)
            let quarter = (month - 1) / 3 + 1
            let year = Calendar.current.component(.year, from: start)
            return "\(year)年Q\(quarter)"
        case .yearly:
            formatter.dateFormat = "yyyy年"
            return formatter.string(from: start)
        }
    }
}

// MARK: - 洞察行组件
struct InsightRow: View {
    let icon: String
    let title: String
    let description: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: Habit.self, HabitLog.self, configurations: config)

    let habit = Habit(name: "阅读", colorHex: "#007AFF")
    container.mainContext.insert(habit)

    return PeriodAnalysisView(habit: habit)
        .modelContainer(container)
}
