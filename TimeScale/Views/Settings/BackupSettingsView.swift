//
//  BackupSettingsView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI
import SwiftData

struct BackupSettingsView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var backupManager = iCloudBackupManager.shared
    @StateObject private var dataManager = DataManager.shared
    
    @State private var showingBackupAlert = false
    @State private var showingRestoreAlert = false
    @State private var showingDeleteAlert = false
    @State private var selectedBackup: BackupMetadata?
    @State private var alertMessage = ""
    @State private var exportURL: URL?
    
    var body: some View {
        NavigationView {
            List {
                // iCloud状态部分
                iCloudStatusSection
                
                // 备份操作部分
                backupActionsSection
                
                // 自动同步设置
                autoSyncSection
                
                // 可用备份列表
                availableBackupsSection
                
                // 本地导出部分
                localExportSection
            }
            .navigationTitle("数据备份")
            .refreshable {
                await refreshBackups()
            }
            .onAppear {
                Task {
                    await refreshBackups()
                }
            }
            .alert("操作结果", isPresented: $showingBackupAlert) {
                Button("确定") { }
            } message: {
                Text(alertMessage)
            }
            .alert("确认恢复", isPresented: $showingRestoreAlert) {
                Button("取消", role: .cancel) { }
                Button("恢复", role: .destructive) {
                    if let backup = selectedBackup {
                        Task {
                            await restoreFromBackup(backup)
                        }
                    }
                }
            } message: {
                if let backup = selectedBackup {
                    Text("确定要从 \(backup.formattedDate) 的备份恢复数据吗？这将替换当前所有数据。")
                }
            }
            .alert("确认删除", isPresented: $showingDeleteAlert) {
                Button("取消", role: .cancel) { }
                Button("删除", role: .destructive) {
                    if let backup = selectedBackup {
                        Task {
                            await deleteBackup(backup)
                        }
                    }
                }
            } message: {
                if let backup = selectedBackup {
                    Text("确定要删除 \(backup.formattedDate) 的备份吗？此操作不可撤销。")
                }
            }
            .sheet(item: Binding<ShareableURL?>(
                get: { exportURL.map(ShareableURL.init) },
                set: { _ in exportURL = nil }
            )) { shareableURL in
                ShareSheet(items: [shareableURL.url])
            }
        }
    }
    
    // MARK: - iCloud状态部分
    private var iCloudStatusSection: some View {
        Section {
            HStack {
                Image(systemName: backupManager.iCloudAvailable ? "icloud.fill" : "icloud.slash")
                    .foregroundColor(backupManager.iCloudAvailable ? .blue : .red)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("iCloud状态")
                        .font(.headline)
                    Text(backupManager.iCloudAvailable ? "已连接" : "不可用")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if backupManager.iCloudAvailable {
                    VStack(alignment: .trailing, spacing: 4) {
                        if let lastBackup = backupManager.lastBackupDate {
                            Text("上次备份")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(RelativeDateTimeFormatter().localizedString(for: lastBackup, relativeTo: Date()))
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        } else {
                            Text("未备份")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        } header: {
            Text("云端备份")
        }
    }
    
    // MARK: - 备份操作部分
    private var backupActionsSection: some View {
        Section {
            // 立即备份按钮
            Button(action: {
                Task {
                    await performBackup()
                }
            }) {
                HStack {
                    Image(systemName: "icloud.and.arrow.up")
                        .foregroundColor(.blue)
                    Text("立即备份")
                    Spacer()
                    if backupManager.isBackingUp {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
            }
            .disabled(!backupManager.iCloudAvailable || backupManager.isBackingUp)
            
            // 刷新备份列表
            Button(action: {
                Task {
                    await refreshBackups()
                }
            }) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                        .foregroundColor(.green)
                    Text("刷新备份列表")
                    Spacer()
                }
            }
            .disabled(!backupManager.iCloudAvailable)
            
        } header: {
            Text("备份操作")
        }
    }
    
    // MARK: - 自动同步设置
    private var autoSyncSection: some View {
        Section {
            Toggle(isOn: $backupManager.syncEnabled) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                        .foregroundColor(.orange)
                    VStack(alignment: .leading, spacing: 2) {
                        Text("自动同步")
                        Text("应用启动时自动备份数据")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .disabled(!backupManager.iCloudAvailable)
            .onChange(of: backupManager.syncEnabled) { _, newValue in
                backupManager.setSyncEnabled(newValue)
            }
            
            if backupManager.syncEnabled, let lastSync = backupManager.lastSyncDate {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.secondary)
                    Text("上次同步")
                    Spacer()
                    Text(RelativeDateTimeFormatter().localizedString(for: lastSync, relativeTo: Date()))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
        } header: {
            Text("同步设置")
        }
    }
    
    // MARK: - 可用备份列表
    private var availableBackupsSection: some View {
        Section {
            if backupManager.availableBackups.isEmpty {
                HStack {
                    Image(systemName: "tray")
                        .foregroundColor(.secondary)
                    Text("暂无备份")
                        .foregroundColor(.secondary)
                }
            } else {
                ForEach(backupManager.availableBackups) { backup in
                    BackupRowView(backup: backup) {
                        selectedBackup = backup
                        showingRestoreAlert = true
                    } onDelete: {
                        selectedBackup = backup
                        showingDeleteAlert = true
                    }
                }
            }
        } header: {
            Text("可用备份 (\(backupManager.availableBackups.count))")
        }
    }
    
    // MARK: - 本地导出部分
    private var localExportSection: some View {
        Section {
            Button(action: {
                Task {
                    await exportLocalData()
                }
            }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(.purple)
                    Text("导出到文件")
                    Spacer()
                    if dataManager.isExporting {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
            }
            .disabled(dataManager.isExporting)
            
        } header: {
            Text("本地导出")
        } footer: {
            Text("导出数据到本地文件，可用于手动备份或迁移到其他设备")
        }
    }
    
    // MARK: - 操作方法
    
    private func performBackup() async {
        do {
            try await backupManager.performBackup(from: modelContext)
            alertMessage = "备份成功完成"
            showingBackupAlert = true
        } catch {
            alertMessage = "备份失败：\(error.localizedDescription)"
            showingBackupAlert = true
        }
    }
    
    private func restoreFromBackup(_ backup: BackupMetadata) async {
        do {
            try await backupManager.restoreFromBackup(backup, to: modelContext, replaceExisting: true)
            alertMessage = "数据恢复成功"
            showingBackupAlert = true
        } catch {
            alertMessage = "恢复失败：\(error.localizedDescription)"
            showingBackupAlert = true
        }
    }
    
    private func deleteBackup(_ backup: BackupMetadata) async {
        do {
            try await backupManager.deleteBackup(backup)
            alertMessage = "备份删除成功"
            showingBackupAlert = true
        } catch {
            alertMessage = "删除失败：\(error.localizedDescription)"
            showingBackupAlert = true
        }
    }
    
    private func refreshBackups() async {
        do {
            try await backupManager.refreshAvailableBackups()
        } catch {
            alertMessage = "刷新失败：\(error.localizedDescription)"
            showingBackupAlert = true
        }
    }
    
    private func exportLocalData() async {
        do {
            let url = try await dataManager.exportData(from: modelContext)
            exportURL = url
        } catch {
            alertMessage = "导出失败：\(error.localizedDescription)"
            showingBackupAlert = true
        }
    }
}

// MARK: - 备份行视图

struct BackupRowView: View {
    let backup: BackupMetadata
    let onRestore: () -> Void
    let onDelete: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 主要信息行
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(backup.formattedDate)
                        .font(.headline)

                    HStack(spacing: 12) {
                        Label(backup.formattedSize, systemImage: "doc")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Label(backup.deviceName, systemImage: "iphone")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if backup.isIncremental {
                            Label("增量", systemImage: "arrow.up.circle")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }
                }

                Spacer()

                // 操作按钮
                HStack(spacing: 8) {
                    Button(action: onRestore) {
                        Image(systemName: "arrow.down.circle")
                            .foregroundColor(.blue)
                    }

                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                    }
                }
            }

            // 版本信息
            HStack {
                Text("应用版本: \(backup.version)")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Spacer()

                Text("数据版本: \(backup.dataVersion)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 辅助结构

struct ShareableURL: Identifiable {
    let id = UUID()
    let url: URL
}



// MARK: - 预览

#Preview {
    BackupSettingsView()
        .modelContainer(for: [Habit.self, HabitLog.self, Goal.self, GoalCheckIn.self, TimeCategory.self, TimeEntry.self], inMemory: true)
}
