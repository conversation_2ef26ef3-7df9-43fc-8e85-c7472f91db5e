//
//  InsightsView.swift
//  TimeScale
//
//  Created by MeeY<PERSON> on 2025/8/25.
//

import SwiftUI
import SwiftData
import Charts

struct InsightsView: View {
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject private var tabRouter: Tab<PERSON><PERSON>er
    @Query private var habits: [Habit]
    @Query private var goals: [Goal]
    @Query private var timeEntries: [TimeEntry]

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 概览卡片
                    overviewSection

                    // 快速导航
                    quickNavigationSection

                    // 本周亮点
                    weeklyHighlightsSection
                }
                .padding()
            }
            .navigationTitle(LocalizedStringKey("洞察"))
        }
    }

    // MARK: - 概览部分
    private var overviewSection: some View {
        VStack(spacing: 16) {
            Text(LocalizedStringKey("数据概览"))
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                Button(action: { tabRouter.selectedTab = 0 }) {
                    OverviewCard(
                        title: "活跃习惯",
                        value: "\(activeHabitsCount)",
                        icon: "checkmark.circle.fill",
                        color: .green
                    )
                }.buttonStyle(.plain)

                Button(action: { tabRouter.selectedTab = 1 }) {
                    OverviewCard(
                        title: "进行中目标",
                        value: "\(activeGoalsCount)",
                        icon: "target",
                        color: .blue
                    )
                }.buttonStyle(.plain)

                Button(action: { tabRouter.selectedTab = 2 }) {
                    OverviewCard(
                        title: "本周记录",
                        value: "\(weeklyEntriesCount)",
                        icon: "clock.fill",
                        color: .orange
                    )
                }.buttonStyle(.plain)

                Button(action: { tabRouter.selectedTab = 2 }) {
                    OverviewCard(
                        title: "总时长",
                        value: formatDuration(weeklyTotalTime),
                        icon: "timer",
                        color: .purple
                    )
                }.buttonStyle(.plain)
            }
        }
    }

    // MARK: - 快速导航
    private var quickNavigationSection: some View {
        VStack(spacing: 16) {
            Text(LocalizedStringKey("快速导航"))
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                NavigationLink(destination: WeeklyReportView()) {
                    QuickNavCard(
                        title: NSLocalizedString("时间周报", comment: ""),
                        subtitle: NSLocalizedString("查看详细的时间使用分析", comment: ""),
                        icon: "chart.bar.fill",
                        color: .blue
                    )
                }
                .buttonStyle(.plain)

                NavigationLink(destination: HabitsAnalysisView()) {
                    QuickNavCard(
                        title: NSLocalizedString("习惯分析", comment: ""),
                        subtitle: NSLocalizedString("查看习惯完成趋势和统计", comment: ""),
                        icon: "chart.line.uptrend.xyaxis",
                        color: .green
                    )
                }
                .buttonStyle(.plain)

                NavigationLink(destination: GoalsAnalysisView()) {
                    QuickNavCard(
                        title: NSLocalizedString("目标分析", comment: ""),
                        subtitle: NSLocalizedString("查看目标进度和完成情况", comment: ""),
                        icon: "chart.pie.fill",
                        color: .orange
                    )
                }
                .buttonStyle(.plain)
            }
        }
    }

    // MARK: - 本周亮点
    private var weeklyHighlightsSection: some View {
        VStack(spacing: 16) {
            Text(LocalizedStringKey("本周亮点"))
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            if weeklyHighlights.isEmpty {
                Text(LocalizedStringKey("本周暂无亮点数据"))
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(weeklyHighlights, id: \.title) { highlight in
                        HighlightCard(highlight: highlight)
                    }
                }
            }
        }
    }

    // MARK: - 计算属性
    private var activeHabitsCount: Int {
        habits.filter { !$0.archived }.count
    }

    private var activeGoalsCount: Int {
        goals.filter { !$0.archived }.count
    }

    private var weeklyEntriesCount: Int {
        let calendar = Calendar.current
        let weekStart = calendar.dateInterval(of: .weekOfYear, for: Date())?.start ?? Date()
        return timeEntries.filter { $0.start >= weekStart && !$0.isRunning }.count
    }

    private var weeklyTotalTime: TimeInterval {
        let calendar = Calendar.current
        let weekStart = calendar.dateInterval(of: .weekOfYear, for: Date())?.start ?? Date()
        return timeEntries
            .filter { $0.start >= weekStart && !$0.isRunning }
            .reduce(0) { $0 + $1.durationSec }
    }

    private var weeklyHighlights: [WeeklyHighlight] {
        var highlights: [WeeklyHighlight] = []

        // 最长连续习惯
        if let bestHabit = habits.max(by: { $0.currentStreak < $1.currentStreak }),
           bestHabit.currentStreak > 0 {
            highlights.append(WeeklyHighlight(
                title: NSLocalizedString("最佳习惯", comment: ""),
                subtitle: String(format: NSLocalizedString("%@ 连续 %lld 天", comment: ""), bestHabit.name, bestHabit.currentStreak),
                icon: "star.fill",
                color: .yellow
            ))
        }

        // 最高完成度目标
        if let bestGoal = goals.max(by: { $0.currentProgress < $1.currentProgress }),
           bestGoal.currentProgress > 0 {
            highlights.append(WeeklyHighlight(
                title: NSLocalizedString("进度最佳", comment: ""),
                subtitle: String(format: NSLocalizedString("%@ 完成 %lld%%", comment: ""), bestGoal.title, Int64(bestGoal.currentProgress * 100)),
                icon: "target",
                color: .blue
            ))
        }

        // 时间使用最多的分类
        if weeklyTotalTime > 0 {
            let categoryTimes = Dictionary(grouping: timeEntries.filter {
                let calendar = Calendar.current
                let weekStart = calendar.dateInterval(of: .weekOfYear, for: Date())?.start ?? Date()
                return $0.start >= weekStart && !$0.isRunning
            }) { $0.category }

            if let topCategory = categoryTimes.max(by: {
                $0.value.reduce(0) { $0 + $1.durationSec } < $1.value.reduce(0) { $0 + $1.durationSec }
            })?.key {
                let duration = categoryTimes[topCategory]?.reduce(0) { $0 + $1.durationSec } ?? 0
                highlights.append(WeeklyHighlight(
                    title: NSLocalizedString("时间投入最多", comment: ""),
                    subtitle: String(format: NSLocalizedString("%@ %@", comment: ""), topCategory.name, formatDuration(duration)),
                    icon: "clock.fill",
                    color: .orange
                ))
            }
        }

        return highlights
    }

    // MARK: - 辅助方法
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60

        if hours > 0 {
            return "\(hours)h\(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

// MARK: - 数据结构
struct WeeklyHighlight {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
}

// MARK: - 组件视图
struct OverviewCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)

                Spacer()
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

struct QuickNavCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(color.opacity(0.2))
                .cornerRadius(8)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
}

struct HighlightCard: View {
    let highlight: WeeklyHighlight

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: highlight.icon)
                .font(.title3)
                .foregroundColor(highlight.color)
                .frame(width: 32, height: 32)
                .background(highlight.color.opacity(0.2))
                .cornerRadius(6)

            VStack(alignment: .leading, spacing: 2) {
                Text(highlight.title)
                    .font(.body)
                    .fontWeight(.medium)

                Text(highlight.subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding()
        .background(Color.white)
        .cornerRadius(8)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 习惯分析视图（基础版）
// 修改 - 实现可选择习惯并展示近90天热力图与 streak 汇总（P0）。Confirmed via mcp-feedback-enhanced
struct HabitsAnalysisView: View {
    @Query private var habits: [Habit]
    @State private var selectedHabitId: UUID?

    // 最近90天日期范围
    private var dateRange: [Date] {
        let calendar = Calendar.current
        let endDate = calendar.startOfDay(for: Date())
        let startDate = calendar.date(byAdding: .day, value: -89, to: endDate) ?? endDate
        var dates: [Date] = []
        var currentDate = startDate
        while currentDate <= endDate {
            dates.append(currentDate)
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
        return dates
    }

    private var selectedHabit: Habit? {
        if let id = selectedHabitId { return habits.first(where: { $0.id == id }) }
        return habits.first
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 选择器
                if habits.isEmpty {
                    Text("暂无习惯数据")
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                } else {
                    Picker("选择习惯", selection: Binding(
                        get: { selectedHabitId ?? habits.first?.id },
                        set: { selectedHabitId = $0 }
                    )) {
                        ForEach(habits) { h in
                            Text(h.name).tag(Optional(h.id))
                        }
                    }
                    .pickerStyle(.menu)
                }

                if let habit = selectedHabit {
                    // 概览卡片
                    HStack(spacing: 12) {
                        StatCard(title: "当前连续", value: "\(habit.currentStreak)", unit: "天", color: Color(hex: habit.colorHex))
                        StatCard(title: "最长连续", value: "\(habit.longestStreak)", unit: "天", color: .orange)
                    }

                    // 热力图
                    VStack(alignment: .leading, spacing: 12) {
                        Text(LocalizedStringKey("最近90天"))
                            .font(.headline)
                        HabitHeatmapView(habit: habit, dateRange: dateRange)
                    }

                    // 月度完成趋势（最近12个月）
                    VStack(alignment: .leading, spacing: 12) {
                        Text(LocalizedStringKey("月度完成趋势"))
                            .font(.headline)
                        HabitMonthlyTrendChart(habit: habit)
                            .frame(height: 200)
                            .background(Color.gray.opacity(0.05))
                            .cornerRadius(12)
                    }
                }
            }
            .padding()
        }
        .navigationTitle("习惯分析")
    }
}

// MARK: - 目标分析视图（基础版）
// 修改 - 实现按进度排序的目标列表与剩余天数（P0）。Confirmed via mcp-feedback-enhanced
struct GoalsAnalysisView: View {
    @Query private var goals: [Goal]

    private var sortedGoals: [Goal] {
        goals.filter { !$0.archived }.sorted { $0.currentProgress > $1.currentProgress }
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 12) {
                if sortedGoals.isEmpty {
                    Text("暂无目标数据")
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                } else {
                    // 目标进度柱状图
                    VStack(alignment: .leading, spacing: 8) {
                        Text("目标进度对比")
                            .font(.headline)
                        Chart(sortedGoals, id: \.id) { goal in
                            BarMark(
                                x: .value("目标", goal.title),
                                y: .value("完成度", min(goal.currentProgress, 1.0))
                            )
                            .foregroundStyle(.blue)
                        }
                        .frame(height: 220)
                        .chartYAxis {
                            AxisMarks(position: .leading) { value in
                                if let v = value.as(Double.self) {
                                    AxisValueLabel("\(Int(v * 100))%")
                                }
                            }
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.05))
                    .cornerRadius(12)

                    ForEach(sortedGoals) { goal in
                        GoalProgressRow(goal: goal)
                    }
                }
            }
            .padding()
        }
        .navigationTitle("目标分析")
    }
}

// MARK: - 目标进度行
private struct GoalProgressRow: View {
    let goal: Goal

    private var progressPercent: Int { Int(goal.currentProgress * 100) }

    private var remainingDaysText: String {
        guard let deadline = goal.deadline else { return "无截止" }
        let days = Calendar.current.dateComponents([.day], from: .now, to: deadline).day ?? 0
        if days < 0 { return "已过期" }
        return "剩余 \(days) 天"
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(goal.title).font(.headline)
                Spacer()
                Text("\(progressPercent)%").font(.subheadline).foregroundColor(.secondary)
            }
            ProgressView(value: min(goal.currentProgress, 1.0))
                .tint(.blue)
            HStack(spacing: 8) {
                Text("目标：\(Int(goal.target)) \(goal.unit)")
                    .font(.caption)
                    .foregroundColor(.secondary)


                Spacer()
                Text(remainingDaysText)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}


// MARK: - 习惯月度趋势图
// 新增 - 使用 Charts 绘制最近12个月每月完成天数折线图（基于 Habit.logs）。Confirmed via mcp-feedback-enhanced
private struct HabitMonthlyTrendChart: View {
    let habit: Habit

    private struct MonthData: Identifiable { let id = UUID(); let monthStart: Date; let count: Int }

    private var data: [MonthData] {
        let calendar = Calendar.current
        let now = Date()
        var months: [Date] = []
        if let startOfCurrentMonth = calendar.dateInterval(of: .month, for: now)?.start {
            for i in (0..<12).reversed() {
                if let start = calendar.date(byAdding: .month, value: -i, to: startOfCurrentMonth) {
                    months.append(start)
                }
            }
        }
        return months.map { monthStart in
            let nextStart = calendar.date(byAdding: .month, value: 1, to: monthStart) ?? monthStart
            let count = habit.logs.filter { $0.done && $0.date >= monthStart && $0.date < nextStart }.count
            return MonthData(monthStart: monthStart, count: count)
        }
    }

    var body: some View {
        Chart(data) { item in
            LineMark(
                x: .value("月份", item.monthStart, unit: .month),
                y: .value("完成天数", item.count)
            )
            PointMark(
                x: .value("月份", item.monthStart, unit: .month),
                y: .value("完成天数", item.count)
            )
        }
        .chartXAxis {
            AxisMarks(values: .stride(by: .month)) { value in
                AxisValueLabel(format: .dateTime.month(.abbreviated))
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading)
        }
    }
}

#Preview {
    InsightsView()
        .modelContainer(for: [Habit.self, Goal.self, TimeEntry.self], inMemory: true)
}
