//
//  Models.swift
//  TimeScale
//
//  Created by <PERSON>e<PERSON><PERSON> on 2025/8/12.
//

import Foundation
import SwiftData

// MARK: - 习惯养成相关模型

/// 重复规则枚举
enum ScheduleRule: String, Codable, CaseIterable {
    case daily = "daily"           // 每日
    case weekdays = "weekdays"     // 工作日
    case custom = "custom"         // 自定义（指定周几）

    var displayName: String {
        switch self {
        case .daily: return "每日"
        case .weekdays: return "工作日"
        case .custom: return "自定义"
        }
    }
}

/// 习惯模型
@Model
final class Habit {
    @Attribute(.unique) var id: UUID
    var name: String
    var colorHex: String
    var scheduleRule: ScheduleRule
    // 修改 - 使用字符串存储自定义周几，避免SwiftData Array<Int>问题
    private var customWeekdaysString: String
    var reminderTimes: [DateComponents]
    var archived: Bool
    var createdAt: Date
    var updatedAt: Date

    @Relationship(deleteRule: .cascade, inverse: \HabitLog.habit)
    var logs: [HabitLog]

    // 计算属性 - 提供便捷的数组访问接口
    var customWeekdays: [Int] {
        get {
            guard !customWeekdaysString.isEmpty else { return [] }
            return customWeekdaysString.split(separator: ",").compactMap { Int($0) }
        }
        set {
            customWeekdaysString = newValue.map { String($0) }.joined(separator: ",")
        }
    }

    init(name: String, colorHex: String = "#007AFF", scheduleRule: ScheduleRule = .daily) {
        self.id = UUID()
        self.name = name
        self.colorHex = colorHex
        self.scheduleRule = scheduleRule
        self.customWeekdaysString = ""
        self.reminderTimes = []
        self.archived = false
        self.createdAt = .now
        self.updatedAt = .now
        self.logs = []
    }
}

/// 习惯打卡记录
@Model
final class HabitLog {
    @Attribute(.unique) var id: UUID
    var date: Date
    var done: Bool
    var note: String?
    var createdAt: Date

    @Relationship var habit: Habit?

    init(date: Date, done: Bool = true, note: String? = nil) {
        self.id = UUID()
        self.date = Calendar.current.startOfDay(for: date)
        self.done = done
        self.note = note
        self.createdAt = .now
    }
}

// MARK: - 目标打卡相关模型

/// 目标周期枚举
enum GoalPeriod: String, Codable, CaseIterable {
    case weekly = "weekly"
    case monthly = "monthly"
    case custom = "custom"

    var displayName: String {
        switch self {
        case .weekly: return "每周"
        case .monthly: return "每月"
        case .custom: return "自定义"
        }
    }
}

/// 目标模型
@Model
final class Goal {
    @Attribute(.unique) var id: UUID
    var title: String
    var unit: String // 次/分钟/公里/页等
    var period: GoalPeriod
    var target: Double
    var deadline: Date?
    var archived: Bool
    var createdAt: Date
    var updatedAt: Date

    @Relationship(deleteRule: .cascade, inverse: \GoalCheckIn.goal)
    var checkIns: [GoalCheckIn]

    init(title: String, unit: String, period: GoalPeriod, target: Double, deadline: Date? = nil) {
        self.id = UUID()
        self.title = title
        self.unit = unit
        self.period = period
        self.target = target
        self.deadline = deadline
        self.archived = false
        self.createdAt = .now
        self.updatedAt = .now
        self.checkIns = []
    }
}

/// 目标打卡记录
@Model
final class GoalCheckIn {
    @Attribute(.unique) var id: UUID
    var timestamp: Date
    var amount: Double
    var note: String?

    @Relationship var goal: Goal?

    init(amount: Double, note: String? = nil) {
        self.id = UUID()
        self.timestamp = .now
        self.amount = amount
        self.note = note
    }
}

// MARK: - 时间记录相关模型

/// 时间分类
@Model
final class TimeCategory {
    @Attribute(.unique) var id: UUID
    var name: String
    var colorHex: String
    var order: Int
    var archived: Bool
    var createdAt: Date

    @Relationship(deleteRule: .cascade, inverse: \TimeEntry.category)
    var entries: [TimeEntry]

    init(name: String, colorHex: String, order: Int = 0) {
        self.id = UUID()
        self.name = name
        self.colorHex = colorHex
        self.order = order
        self.archived = false
        self.createdAt = .now
        self.entries = []
    }
}

/// 时间记录条目
@Model
final class TimeEntry {
    @Attribute(.unique) var id: UUID
    var start: Date
    var end: Date?
    var durationSec: Double
    var note: String?
    var isRunning: Bool // 是否正在计时中
    var createdAt: Date

    @Relationship var category: TimeCategory?

    init(start: Date = .now, category: TimeCategory? = nil, note: String? = nil) {
        self.id = UUID()
        self.start = start
        self.end = nil
        self.durationSec = 0
        self.note = note
        self.isRunning = true
        self.createdAt = .now
        self.category = category
    }

    /// 结束计时
    func stopTimer() {
        guard isRunning else { return }
        let endTime = Date.now
        self.end = endTime
        self.durationSec = endTime.timeIntervalSince(start)
        self.isRunning = false
    }

    /// 计算当前持续时间（秒）
    var currentDuration: Double {
        if isRunning {
            return Date.now.timeIntervalSince(start)
        } else {
            return durationSec
        }
    }
}

// MARK: - 扩展方法

extension Habit {
    /// 计算当前连续天数（如果今天已完成）
    var currentStreak: Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: .now)

        // 检查今天是否已完成
        let todayCompleted = logs.contains { log in
            calendar.isDate(log.date, inSameDayAs: today) && log.done
        }

        // 如果今天未完成，返回0
        guard todayCompleted else { return 0 }

        // 从今天开始往前计算连续天数
        var streak = 0
        var checkDate = today

        while true {
            if let log = logs.first(where: { calendar.isDate($0.date, inSameDayAs: checkDate) }),
               log.done {
                streak += 1
                checkDate = calendar.date(byAdding: .day, value: -1, to: checkDate) ?? checkDate
            } else {
                break
            }
        }

        return streak
    }

    /// 新增 - 计算之前的连续天数（今天未完成但之前有连续）
    var previousStreak: Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: .now)

        // 检查今天是否已完成
        let todayCompleted = logs.contains { log in
            calendar.isDate(log.date, inSameDayAs: today) && log.done
        }

        // 如果今天已完成，返回0（应该使用currentStreak）
        guard !todayCompleted else { return 0 }

        // 从昨天开始往前计算连续天数
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: today) else { return 0 }

        var streak = 0
        var checkDate = yesterday

        while true {
            if let log = logs.first(where: { calendar.isDate($0.date, inSameDayAs: checkDate) }),
               log.done {
                streak += 1
                checkDate = calendar.date(byAdding: .day, value: -1, to: checkDate) ?? checkDate
            } else {
                break
            }
        }

        return streak
    }

    /// 新增 - 计算已中断天数（从最后一次连续打卡结束到今天）
    var daysSinceLastStreak: Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: .now)

        // 检查今天是否已完成
        let todayCompleted = logs.contains { log in
            calendar.isDate(log.date, inSameDayAs: today) && log.done
        }

        // 如果今天已完成，返回0（没有中断）
        guard !todayCompleted else { return 0 }

        // 如果之前没有连续记录，返回0
        guard previousStreak > 0 else { return 0 }

        // 找到最后一次连续打卡的结束日期
        let sortedLogs = logs.filter(\.done).sorted { $0.date > $1.date }

        // 从昨天开始往前找连续的最后一天
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: today) else { return 0 }

        var checkDate = yesterday
        var lastStreakEndDate: Date?

        // 找到连续打卡的最后一天
        while true {
            if let log = logs.first(where: { calendar.isDate($0.date, inSameDayAs: checkDate) }),
               log.done {
                lastStreakEndDate = checkDate
                checkDate = calendar.date(byAdding: .day, value: -1, to: checkDate) ?? checkDate
            } else {
                break
            }
        }

        // 计算从最后一次打卡到今天的天数
        guard let endDate = lastStreakEndDate else { return 0 }

        let daysBetween = calendar.dateComponents([.day], from: endDate, to: today).day ?? 0
        return max(0, daysBetween - 1) // 减1是因为不包括最后打卡的那一天
    }

    /// 计算历史最长连续天数
    var longestStreak: Int {
        let calendar = Calendar.current
        let sortedLogs = logs.filter(\.done).sorted { $0.date < $1.date }

        var maxStreak = 0
        var currentStreak = 0
        var lastDate: Date?

        for log in sortedLogs {
            if let last = lastDate,
               calendar.dateInterval(of: .day, for: last)?.end == calendar.dateInterval(of: .day, for: log.date)?.start {
                currentStreak += 1
            } else {
                currentStreak = 1
            }

            maxStreak = max(maxStreak, currentStreak)
            lastDate = log.date
        }

        return maxStreak
    }
}

extension Goal {
    /// 计算当前周期的进度
    var currentProgress: Double {
        let calendar = Calendar.current
        let now = Date.now

        let startDate: Date
        switch period {
        case .weekly:
            startDate = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
        case .monthly:
            startDate = calendar.dateInterval(of: .month, for: now)?.start ?? now
        case .custom:
            startDate = deadline?.addingTimeInterval(-30*24*3600) ?? now // 默认30天前
        }

        let totalAmount = checkIns
            .filter { $0.timestamp >= startDate }
            .reduce(0) { $0 + $1.amount }

        return min(totalAmount / target, 1.0)
    }
}
